# VoicePilot 設定指南

## 快速開始

1. **啟動應用程式**
   ```bash
   npm start
   ```

2. **配置Azure OpenAI**
   - 在系統托盤中找到VoicePilot圖示
   - 右鍵點擊選擇"Settings"
   - 填入你的Azure OpenAI設定

3. **開始使用**
   - 在任何應用程式中點擊文字輸入欄位
   - 按下 `Ctrl+Shift+V` 開始錄音
   - 再次按下 `Ctrl+Shift+V` 停止錄音
   - AI會處理你的語音並將結果複製到剪貼簿
   - 按下 `Ctrl+V` 貼上結果

## Azure OpenAI 設定

### 1. 取得Azure OpenAI資源

1. 登入 [Azure Portal](https://portal.azure.com)
2. 建立新的 "Azure OpenAI" 資源
3. 等待資源部署完成

### 2. 取得API資訊

1. 進入你的Azure OpenAI資源
2. 在左側選單中點擊 "Keys and Endpoint"
3. 複製以下資訊：
   - **Endpoint**: 類似 `https://your-resource.openai.azure.com`
   - **Key**: API金鑰（Key 1 或 Key 2）

### 3. 部署模型

1. 在Azure OpenAI資源中，點擊 "Model deployments"
2. 點擊 "Create new deployment"
3. 選擇以下模型：
   - **GPT-4o Mini** (建議用於文字處理)
   - **Whisper** (用於語音轉文字)

### 4. 在VoicePilot中設定

1. 啟動VoicePilot
2. 右鍵點擊系統托盤圖示，選擇"Settings"
3. 填入：
   - **Azure OpenAI Endpoint**: 你的endpoint URL
   - **Azure API Key**: 你的API金鑰
   - **AI模型**: 選擇 gpt-4o-mini
4. 點擊"測試連接"確認設定正確
5. 點擊"儲存設定"

## 使用技巧

### 語音指令範例

- **直接輸入**: "請輸入我的電子郵件地址 <EMAIL>"
- **寫作任務**: "寫一篇關於人工智慧的100字文章"
- **郵件撰寫**: "寫一封感謝信給客戶，感謝他們的支持"
- **翻譯**: "把這段文字翻譯成英文：你好世界"
- **格式化**: "把以下項目整理成列表：蘋果、香蕉、橘子"

### 快捷鍵

- **Ctrl+Shift+V**: 開始/停止錄音（可在設定中修改）
- **Ctrl+V**: 貼上AI回應

### 故障排除

1. **快捷鍵不工作**
   - 檢查是否有其他應用程式使用相同快捷鍵
   - 嘗試更換不同的快捷鍵組合

2. **AI回應錯誤**
   - 檢查Azure OpenAI設定是否正確
   - 確認API金鑰有效
   - 檢查網路連接

3. **語音錄製問題**
   - 確保麥克風權限已開啟
   - 檢查系統音訊設定

## 開發模式

如果你想要修改或擴展VoicePilot：

```bash
# 開發模式啟動
npm run dev

# 建置應用程式
npm run build:win  # Windows
npm run build:mac  # macOS
```

## 系統需求

- Windows 10+ 或 macOS 10.14+
- Node.js 16+
- 有效的Azure OpenAI訂閱
- 麥克風權限

## 注意事項

- 目前版本使用剪貼簿作為文字輸入方式
- 語音錄製功能正在開發中，目前為模擬版本
- 建議在安靜環境中使用以獲得最佳語音識別效果
