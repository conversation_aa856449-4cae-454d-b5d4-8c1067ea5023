const { clipboard } = require('electron');

class TextInputService {
  constructor() {
    // 使用剪貼簿作為文字輸入的替代方案
    this.useClipboard = true;
  }

  async typeText(text) {
    try {
      if (!text || typeof text !== 'string') {
        throw new Error('Invalid text input');
      }

      console.log('Copying text to clipboard:', text.substring(0, 50) + '...');

      // 將文字複製到剪貼簿
      clipboard.writeText(text);

      console.log('Text copied to clipboard. Please paste with Ctrl+V');

      // 顯示通知給用戶
      this.showNotification('Text ready! Press Ctrl+V to paste.');

    } catch (error) {
      console.error('Error copying text:', error);
      throw error;
    }
  }

  splitTextIntoChunks(text, chunkSize) {
    const chunks = [];
    for (let i = 0; i < text.length; i += chunkSize) {
      chunks.push(text.substring(i, i + chunkSize));
    }
    return chunks;
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 顯示系統通知
  showNotification(message) {
    try {
      const { Notification } = require('electron');

      if (Notification.isSupported()) {
        new Notification({
          title: 'VoicePilot',
          body: message,
          silent: false
        }).show();
      }
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  // 簡化版本：直接使用剪貼簿
  async insertText(text) {
    return this.typeText(text);
  }

  async replaceSelectedText(text) {
    return this.typeText(text);
  }

  async smartTypeText(text) {
    return this.typeText(text);
  }
}

module.exports = TextInputService;
