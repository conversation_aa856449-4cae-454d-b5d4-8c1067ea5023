## <small>0.5.1 (2018-03-03)</small>

* 0.5.1 ([b9b0ab6](https://github.com/octalmage/robotjs/commit/b9b0ab6))



## 0.5.0 (2018-03-03)

* 0.5.0 ([0687a34](https://github.com/octalmage/robotjs/commit/0687a34))
* Another attempt. ([f5f4a60](https://github.com/octalmage/robotjs/commit/f5f4a60))
* Only build on tag. ([3b8cf98](https://github.com/octalmage/robotjs/commit/3b8cf98))
* Only run on master. ([91a5397](https://github.com/octalmage/robotjs/commit/91a5397))
* Pass token through npm. ([b69246c](https://github.com/octalmage/robotjs/commit/b69246c))
* Should be tag name. ([eb1c889](https://github.com/octalmage/robotjs/commit/eb1c889))
* Try using defined. ([dd96c25](https://github.com/octalmage/robotjs/commit/dd96c25))
* Upload Windows build. ([5d92169](https://github.com/octalmage/robotjs/commit/5d92169))
* Use EQU instead. ([755bcd1](https://github.com/octalmage/robotjs/commit/755bcd1))
* Use on_success. ([e7dc303](https://github.com/octalmage/robotjs/commit/e7dc303))



## <small>0.4.8 (2018-02-26)</small>

* - package lock ([34d3653](https://github.com/octalmage/robotjs/commit/34d3653))
* Add integration tests. ([c29d20d](https://github.com/octalmage/robotjs/commit/c29d20d))
* Add test for screen. ([eb11444](https://github.com/octalmage/robotjs/commit/eb11444))
* Added right_control and left_control to key flags as well as right_shift ([33f838d](https://github.com/octalmage/robotjs/commit/33f838d))
* Allow negative coordinates for mouseMove and dragMouse ([bb63305](https://github.com/octalmage/robotjs/commit/bb63305))
* Cat for testing. ([02a7f94](https://github.com/octalmage/robotjs/commit/02a7f94))
* Change to prebuild command. ([895db63](https://github.com/octalmage/robotjs/commit/895db63))
* Deploy scripts. ([9c26d95](https://github.com/octalmage/robotjs/commit/9c26d95))
* Finish Jasmine migration. ([98eb2ca](https://github.com/octalmage/robotjs/commit/98eb2ca))
* Fix API URL ([a2f6da3](https://github.com/octalmage/robotjs/commit/a2f6da3))
* Fix comment. ([f2bbeab](https://github.com/octalmage/robotjs/commit/f2bbeab))
* Fix createStringForKey failure on Mac OS for non-Latin languages ([0045e18](https://github.com/octalmage/robotjs/commit/0045e18))
* Fix for Windows. ([facc34f](https://github.com/octalmage/robotjs/commit/facc34f))
* Fix scrolling issue. ([d95866f](https://github.com/octalmage/robotjs/commit/d95866f))
* Fix test for Linux. ([643b925](https://github.com/octalmage/robotjs/commit/643b925))
* Fixed Linux Y-axis scrolling bug ([798f761](https://github.com/octalmage/robotjs/commit/798f761))
* Flip x on Windows to match other platforms. ([db15ff7](https://github.com/octalmage/robotjs/commit/db15ff7))
* horizontal scroll test. ([0ebd39e](https://github.com/octalmage/robotjs/commit/0ebd39e))
* Increase timeout. ([4c053ea](https://github.com/octalmage/robotjs/commit/4c053ea))
* Jasmine migration in progress. ([24ed91d](https://github.com/octalmage/robotjs/commit/24ed91d))
* Link in readme to docs goes straight to website ([64f5400](https://github.com/octalmage/robotjs/commit/64f5400))
* Move keyboard tests to it's own file. ([37e3165](https://github.com/octalmage/robotjs/commit/37e3165))
* Remove old header tags. ([da920c2](https://github.com/octalmage/robotjs/commit/da920c2))
* Remove unsupported Node versions. ([112a9a4](https://github.com/octalmage/robotjs/commit/112a9a4))
* Remove unsupported Node.js versions for Appveyor. ([452bd6c](https://github.com/octalmage/robotjs/commit/452bd6c))
* Support additional keys (capslock, numpad-keys, right alt+control). ([55444c3](https://github.com/octalmage/robotjs/commit/55444c3)), closes [#358](https://github.com/octalmage/robotjs/issues/358)
* Support older versions of Node. ([07d6f0e](https://github.com/octalmage/robotjs/commit/07d6f0e))
* Test cleanup. ([cc03080](https://github.com/octalmage/robotjs/commit/cc03080))
* The quotes break tests on windows. ([1f80fb1](https://github.com/octalmage/robotjs/commit/1f80fb1))
* Update package.json to remove tape. ([6584c95](https://github.com/octalmage/robotjs/commit/6584c95))
* Update screen file. ([23f9513](https://github.com/octalmage/robotjs/commit/23f9513))
* Update scroll mouse for 0.4.6 ([cdb0b76](https://github.com/octalmage/robotjs/commit/cdb0b76)), closes [#194](https://github.com/octalmage/robotjs/issues/194)
* Update target practice. ([092f3c7](https://github.com/octalmage/robotjs/commit/092f3c7))
* Updated link to docs in under #API ([f7170e5](https://github.com/octalmage/robotjs/commit/f7170e5))
* Use XSync instead of XFlush everywhere - fixes https://github.com/octalmage/robotjs/issues/347 ([9f3a4c4](https://github.com/octalmage/robotjs/commit/9f3a4c4))
* Windows scrolling bug ([b4869dd](https://github.com/octalmage/robotjs/commit/b4869dd)), closes [#360](https://github.com/octalmage/robotjs/issues/360)



## <small>0.4.7 (2017-03-26)</small>

* 0.4.7 ([06dedb4](https://github.com/octalmage/robotjs/commit/06dedb4))
* Fix trailing comma. ([733caae](https://github.com/octalmage/robotjs/commit/733caae))
* Use prebuild-install. ([7bd769c](https://github.com/octalmage/robotjs/commit/7bd769c))



## <small>0.4.6 (2017-03-26)</small>

* 0.4.6 ([d935e41](https://github.com/octalmage/robotjs/commit/d935e41))
* Accidentally added old scroll code back. ([6f8d2b6](https://github.com/octalmage/robotjs/commit/6f8d2b6))
* Add "menu" key (windows only) ([96220f1](https://github.com/octalmage/robotjs/commit/96220f1))
* Added left and right control keys ([c1f04e0](https://github.com/octalmage/robotjs/commit/c1f04e0))
* Bump prebuild. ([ee210f1](https://github.com/octalmage/robotjs/commit/ee210f1))
* Do not break linux or mac build ([85c5b9f](https://github.com/octalmage/robotjs/commit/85c5b9f))
* Encode to utf-16 for CGEventKeyboardSetUnicodeString ([ea45808](https://github.com/octalmage/robotjs/commit/ea45808))
* Fix errors reported by CI on undeclared variables ([daadbcc](https://github.com/octalmage/robotjs/commit/daadbcc))
* Fixed spacing ([630b198](https://github.com/octalmage/robotjs/commit/630b198))
* Fixed spacing. ([3ff4434](https://github.com/octalmage/robotjs/commit/3ff4434))
* Modified the mouseToggle method signature, since according to the documentation the button parameter ([cfac5ab](https://github.com/octalmage/robotjs/commit/cfac5ab))
* Support Unicode Characters for .typeString() ([7236ff3](https://github.com/octalmage/robotjs/commit/7236ff3))



## <small>0.4.5 (2016-10-16)</small>

* 0.4.5 ([dfd7e60](https://github.com/octalmage/robotjs/commit/dfd7e60))
* Add CONTRIBUTING.md. ([76e56a6](https://github.com/octalmage/robotjs/commit/76e56a6))
* Add f13-24. ([f4854ce](https://github.com/octalmage/robotjs/commit/f4854ce))
* Add ISSUE_TEMPLATE.md. ([30dfce7](https://github.com/octalmage/robotjs/commit/30dfce7))
* Add LICENSE.md. ([60b36ca](https://github.com/octalmage/robotjs/commit/60b36ca))
* Add link to Electron instructions. ([b4adaaa](https://github.com/octalmage/robotjs/commit/b4adaaa))
* Add spaces before comments. ([a9e12ed](https://github.com/octalmage/robotjs/commit/a9e12ed))
* Add typescript declaration file ([56b90e0](https://github.com/octalmage/robotjs/commit/56b90e0))
* Add waffle.io link. ([b4c8108](https://github.com/octalmage/robotjs/commit/b4c8108))
* better argument parsing ([4501a8b](https://github.com/octalmage/robotjs/commit/4501a8b))
* change function style, matching rest of code ([9257211](https://github.com/octalmage/robotjs/commit/9257211))
* Fix merge conflict. ([d6d9e23](https://github.com/octalmage/robotjs/commit/d6d9e23))
* fix os compatibility ([91cd9ce](https://github.com/octalmage/robotjs/commit/91cd9ce))
* Fix tests for higher density screens. ([299a66d](https://github.com/octalmage/robotjs/commit/299a66d))
* Fixed ascii art. ([efa3582](https://github.com/octalmage/robotjs/commit/efa3582))
* Fixed formatting ([9623852](https://github.com/octalmage/robotjs/commit/9623852))
* get and set display name for XOpenDisplay() ([cdbf8df](https://github.com/octalmage/robotjs/commit/cdbf8df))
* improve variable names, move hasDisplayNameChanged reset ([0e457ba](https://github.com/octalmage/robotjs/commit/0e457ba))
* No saving code for now. ([42a2562](https://github.com/octalmage/robotjs/commit/42a2562))
* Remove .save. ([4e0f44c](https://github.com/octalmage/robotjs/commit/4e0f44c))
* Remove bitmap save method. ([f58e519](https://github.com/octalmage/robotjs/commit/f58e519))
* updated jub3i's code to work with new nan, fixes #153 ([3f3dc7b](https://github.com/octalmage/robotjs/commit/3f3dc7b)), closes [#153](https://github.com/octalmage/robotjs/issues/153)
* Use a smarter fix for tests. ([f4707f2](https://github.com/octalmage/robotjs/commit/f4707f2))



## <small>0.4.4 (2016-06-01)</small>

* 0.4.4 ([483b46d](https://github.com/octalmage/robotjs/commit/483b46d))
* Add right_shift. ([666cc6f](https://github.com/octalmage/robotjs/commit/666cc6f))



## <small>0.4.3 (2016-05-30)</small>

* 0.4.3 ([3e5f02b](https://github.com/octalmage/robotjs/commit/3e5f02b))
* Add missing word. ([a76792a](https://github.com/octalmage/robotjs/commit/a76792a))
* Add numpad 0-9. ([afc4863](https://github.com/octalmage/robotjs/commit/afc4863))
* Add numpad tests. ([8e24802](https://github.com/octalmage/robotjs/commit/8e24802))
* Added safety checks. ([ab802ba](https://github.com/octalmage/robotjs/commit/ab802ba))
* Align AppVeyor build versions with Travis CI. ([d2fc806](https://github.com/octalmage/robotjs/commit/d2fc806))
* Build on Mac and Linux. ([efd4ec6](https://github.com/octalmage/robotjs/commit/efd4ec6))
* Changed mouseScroll to use X and Y as direction. ([9917f49](https://github.com/octalmage/robotjs/commit/9917f49))
* Compile and test our module. ([c688f04](https://github.com/octalmage/robotjs/commit/c688f04))
* Don't run numpad tests on Linux. ([d7cc60e](https://github.com/octalmage/robotjs/commit/d7cc60e))
* Fix whitespace. ([041df96](https://github.com/octalmage/robotjs/commit/041df96))
* Fixed error: ‘for’ loop initial declarations are only allowed in C99 mode ([3f3c5c5](https://github.com/octalmage/robotjs/commit/3f3c5c5))
* Fixed old variable left over. ([b6eecbf](https://github.com/octalmage/robotjs/commit/b6eecbf))
* Fixed scroll now using signed ints. ([51082e9](https://github.com/octalmage/robotjs/commit/51082e9))
* Fixed unused variable warning. ([60021fc](https://github.com/octalmage/robotjs/commit/60021fc))
* Ignore prebuilds directory. ([4d003a5](https://github.com/octalmage/robotjs/commit/4d003a5))
* Migrate to SendInput from mouse_event (#181) ([66611b7](https://github.com/octalmage/robotjs/commit/66611b7)), closes [#181](https://github.com/octalmage/robotjs/issues/181)
* New .travis.yml for Mac testing. ([25e4dcf](https://github.com/octalmage/robotjs/commit/25e4dcf))
* New screen capture code. ([fdbbff9](https://github.com/octalmage/robotjs/commit/fdbbff9))
* New sine wave mouse gif. ([6e09549](https://github.com/octalmage/robotjs/commit/6e09549))
* Not sure how this file got here. ([e22fec6](https://github.com/octalmage/robotjs/commit/e22fec6))
* Only start Xvfb on Linux. ([00effc9](https://github.com/octalmage/robotjs/commit/00effc9))
* Readme updates! ([2756b3e](https://github.com/octalmage/robotjs/commit/2756b3e))
* Release mode and pixEnc. ([58f6c52](https://github.com/octalmage/robotjs/commit/58f6c52))
* Remove whitespace. ([cea1cae](https://github.com/octalmage/robotjs/commit/cea1cae))
* Seperate before_install and before_script. ([f0e067a](https://github.com/octalmage/robotjs/commit/f0e067a))
* Space before comment. ([e2ff6cd](https://github.com/octalmage/robotjs/commit/e2ff6cd))
* Test that numpad throws on Linux. ([7543df7](https://github.com/octalmage/robotjs/commit/7543df7))



## <small>0.4.2 (2016-05-08)</small>

* 0.4.2 ([d3226d2](https://github.com/octalmage/robotjs/commit/d3226d2))
* add insert-key ([b910e7e](https://github.com/octalmage/robotjs/commit/b910e7e))
* fix spacing ([7720012](https://github.com/octalmage/robotjs/commit/7720012))
* fixing insert-key for x11 ([3ce729f](https://github.com/octalmage/robotjs/commit/3ce729f))
* fixing whitespaces caused by editor-settings ([037abf3](https://github.com/octalmage/robotjs/commit/037abf3))
* Insert isn't a valid key on Mac. ([ac0d16b](https://github.com/octalmage/robotjs/commit/ac0d16b))
* Migrate to SendInput for Mouse movement ([39f89f0](https://github.com/octalmage/robotjs/commit/39f89f0))
* Revert "Test fix for node 0.8 failing." ([d5e3668](https://github.com/octalmage/robotjs/commit/d5e3668))
* Update to nan 2.2.1. ([54c65c3](https://github.com/octalmage/robotjs/commit/54c65c3))
* Use prebuild. ([7b48ed5](https://github.com/octalmage/robotjs/commit/7b48ed5))



## <small>0.4.1 (2016-04-17)</small>

* 0.4.1 ([e2b7108](https://github.com/octalmage/robotjs/commit/e2b7108))
* Add new test to help identify keycode issues. ([3d8ba52](https://github.com/octalmage/robotjs/commit/3d8ba52))
* Remove keyCodeRequiresSystemDefinedEvent and use new keycodes. ([bfcafa4](https://github.com/octalmage/robotjs/commit/bfcafa4))
* Use new keycode for multimedia keys. ([7a4d90c](https://github.com/octalmage/robotjs/commit/7a4d90c))



## 0.4.0 (2016-04-17)

* 0 is the image, 1 is x, and 2 is y. :| ([c411267](https://github.com/octalmage/robotjs/commit/c411267))
* 0 is the image, 1 is x, and 2 is y. :| ([c41f382](https://github.com/octalmage/robotjs/commit/c41f382))
* 0.4.0 ([9f3818e](https://github.com/octalmage/robotjs/commit/9f3818e))
* Add notifications to TravisCI builds. ([c8cfc1c](https://github.com/octalmage/robotjs/commit/c8cfc1c))
* Add tests for bitmap. ([ab29b45](https://github.com/octalmage/robotjs/commit/ab29b45))
* Added BMP class and buildBMP. ([bcac2ac](https://github.com/octalmage/robotjs/commit/bcac2ac))
* Added KEYEVENTF_EXTENDEDKEY flag when extended keys are triggered on win32 ([8e0cc92](https://github.com/octalmage/robotjs/commit/8e0cc92))
* Added KEYEVENTF_EXTENDEDKEY flag when extended keys are triggered on win32 ([081978e](https://github.com/octalmage/robotjs/commit/081978e))
* Added typeStringDelayed. ([45afde6](https://github.com/octalmage/robotjs/commit/45afde6))
* Allow x, y, width, and height to be passed. ([bb2ab38](https://github.com/octalmage/robotjs/commit/bb2ab38))
* Argument checking for getPixelColor. ([4a5572f](https://github.com/octalmage/robotjs/commit/4a5572f))
* Better error handling for dragMouse. ([93417d3](https://github.com/octalmage/robotjs/commit/93417d3))
* Better error handling for moveMouse. ([dd6e658](https://github.com/octalmage/robotjs/commit/dd6e658))
* Better error handling for moveMouseSmooth. ([2e221ba](https://github.com/octalmage/robotjs/commit/2e221ba))
* Better tests for getPixelColor. ([d8cbb74](https://github.com/octalmage/robotjs/commit/d8cbb74))
* Better tests for moveMouse. ([0f09623](https://github.com/octalmage/robotjs/commit/0f09623))
* Call captureScreen with arguments if passed. ([da59926](https://github.com/octalmage/robotjs/commit/da59926))
* Convert the rest of the spaces. ([f653b92](https://github.com/octalmage/robotjs/commit/f653b92))
* Correct buffer size. ([cb76f5e](https://github.com/octalmage/robotjs/commit/cb76f5e))
* Create calculateDeltas function and use it in dragMouse. ([7a199d4](https://github.com/octalmage/robotjs/commit/7a199d4))
* Create calculateDeltas function and use it in dragMouse. ([ad713f0](https://github.com/octalmage/robotjs/commit/ad713f0))
* Define colorAt at declaration. ([03759cb](https://github.com/octalmage/robotjs/commit/03759cb))
* fix scroll mouse on windows XP, see #97, C89 (2008) variable declaration on top of function ([99a96e0](https://github.com/octalmage/robotjs/commit/99a96e0)), closes [#97](https://github.com/octalmage/robotjs/issues/97)
* Make sure coords are in bounds. ([b1f2852](https://github.com/octalmage/robotjs/commit/b1f2852))
* Map virtual key before triggering keybd_event ([c47f8b0](https://github.com/octalmage/robotjs/commit/c47f8b0))
* Only define calculateDeltas for Mac. ([3eef8cb](https://github.com/octalmage/robotjs/commit/3eef8cb))
* Only define calculateDeltas for Mac. ([f7fac28](https://github.com/octalmage/robotjs/commit/f7fac28))
* Remove extra spaces. ([2ad647b](https://github.com/octalmage/robotjs/commit/2ad647b))
* Remove unnecessary space. ([a764c66](https://github.com/octalmage/robotjs/commit/a764c66))
* Remove unused code. ([6539910](https://github.com/octalmage/robotjs/commit/6539910))
* Run keyboard tests. ([4d2c20d](https://github.com/octalmage/robotjs/commit/4d2c20d))
* Starting tests for keyTap. ([c9063ca](https://github.com/octalmage/robotjs/commit/c9063ca))
* Starting to implement bitmap sharing. ([b58962f](https://github.com/octalmage/robotjs/commit/b58962f))
* Starts at 0. ([0cd6db2](https://github.com/octalmage/robotjs/commit/0cd6db2))
* Test fix for node 0.8 failing. ([eb18a1f](https://github.com/octalmage/robotjs/commit/eb18a1f))
* Tests for dragMouse. ([dfa83f6](https://github.com/octalmage/robotjs/commit/dfa83f6))
* Tests for mouseClick. ([b0cfad0](https://github.com/octalmage/robotjs/commit/b0cfad0))
* Tests for moveMouseSmooth. ([ad7e5dc](https://github.com/octalmage/robotjs/commit/ad7e5dc))
* This commit fixes #148: Volume keys don't work on Mac. ([163feb5](https://github.com/octalmage/robotjs/commit/163feb5)), closes [#148](https://github.com/octalmage/robotjs/issues/148)
* Throw catchable error if requested pixels are outside the bitmap's dimensions. ([d65576b](https://github.com/octalmage/robotjs/commit/d65576b))
* Un-mix up the deltaX and deltaY. ([e3f084e](https://github.com/octalmage/robotjs/commit/e3f084e))
* Un-mix up the deltaX and deltaY. ([1cf70f1](https://github.com/octalmage/robotjs/commit/1cf70f1))
* Update keypress.c ([faddf9c](https://github.com/octalmage/robotjs/commit/faddf9c))
* Updated TravisCI and Appveyor configs to allow 0.8.x failures. ([38f85c0](https://github.com/octalmage/robotjs/commit/38f85c0))
* Use tabs instead of spaces. ([8ecd518](https://github.com/octalmage/robotjs/commit/8ecd518))
* Use tabs instead of spaces. ([47b901f](https://github.com/octalmage/robotjs/commit/47b901f))
* Workaround for games not detecting mouse moves. ([9e3a067](https://github.com/octalmage/robotjs/commit/9e3a067))
* Workaround for games not detecting mouse moves. ([6fd31a8](https://github.com/octalmage/robotjs/commit/6fd31a8))
* TODO: Need tests for mouseToggle, and scrollMouse. ([648fff4](https://github.com/octalmage/robotjs/commit/648fff4))



## <small>0.3.7 (2015-12-29)</small>

* 0.3.7 ([4843f0b](https://github.com/octalmage/robotjs/commit/4843f0b))
* Fixed F9 key ([48c432f](https://github.com/octalmage/robotjs/commit/48c432f))
* Keep code style consistent. ([69ab661](https://github.com/octalmage/robotjs/commit/69ab661))
* Screen Capture Fix. ([bcec0bc](https://github.com/octalmage/robotjs/commit/bcec0bc))
* Updated comments to match code style. ([d5096fe](https://github.com/octalmage/robotjs/commit/d5096fe))
* Use 9999 instead of -1 because CGKeyCode is an unsigned int. ([64e0ecf](https://github.com/octalmage/robotjs/commit/64e0ecf)), closes [#106](https://github.com/octalmage/robotjs/issues/106)



## <small>0.3.6 (2015-12-14)</small>

* 0.3.6 ([1a3df83](https://github.com/octalmage/robotjs/commit/1a3df83))



## <small>0.3.5 (2015-12-07)</small>

* 0.3.5 ([3e9d533](https://github.com/octalmage/robotjs/commit/3e9d533))
* Link to relevant wiki sections. ([727313b](https://github.com/octalmage/robotjs/commit/727313b))
* Prevent unused variable warning for Mac and Linux. ([c3edbc2](https://github.com/octalmage/robotjs/commit/c3edbc2))
* Update README URLs based on HTTP redirects ([2072907](https://github.com/octalmage/robotjs/commit/2072907))



## <small>0.3.4 (2015-11-22)</small>

* 0.3.4 ([4b28b99](https://github.com/octalmage/robotjs/commit/4b28b99))
* Add FAQ section. ([ee0fe6d](https://github.com/octalmage/robotjs/commit/ee0fe6d))
* Add link to create a new issue. ([1e740ec](https://github.com/octalmage/robotjs/commit/1e740ec))
* Add multi-monitor question to FAQ. ([9592dc9](https://github.com/octalmage/robotjs/commit/9592dc9))
* Added dragMouse function ([406acfb](https://github.com/octalmage/robotjs/commit/406acfb)), closes [#127](https://github.com/octalmage/robotjs/issues/127)
* Added support for arrays of key flags ([c2e62d6](https://github.com/octalmage/robotjs/commit/c2e62d6))
* Center badges. ([a264ed8](https://github.com/octalmage/robotjs/commit/a264ed8))
* Center logo. ([b615176](https://github.com/octalmage/robotjs/commit/b615176))
* Change wording a bit. ([721bb8d](https://github.com/octalmage/robotjs/commit/721bb8d))
* Link to Twitter and blog. ([5fd2c51](https://github.com/octalmage/robotjs/commit/5fd2c51))
* Made compatible with older versions of Windows. ([d99f3bb](https://github.com/octalmage/robotjs/commit/d99f3bb))
* Passing constant instead of zero for mouseButton on mouse move ([78603ff](https://github.com/octalmage/robotjs/commit/78603ff))
* Remove blog section. ([9edeefc](https://github.com/octalmage/robotjs/commit/9edeefc))
* Use new blog URL. ([f0a6f2f](https://github.com/octalmage/robotjs/commit/f0a6f2f))
* Using v8::Handle instead of v8::Local for older versions of Node ([e24ee51](https://github.com/octalmage/robotjs/commit/e24ee51))



## <small>0.3.3 (2015-11-02)</small>

* 0.3.3 ([eeb7633](https://github.com/octalmage/robotjs/commit/eeb7633))
* Add some new keywords. ([dd59242](https://github.com/octalmage/robotjs/commit/dd59242))
* Fix for #118. ([570f453](https://github.com/octalmage/robotjs/commit/570f453)), closes [#118](https://github.com/octalmage/robotjs/issues/118)



## <small>0.3.2 (2015-10-28)</small>

* 0.3.2 ([9d5d2e1](https://github.com/octalmage/robotjs/commit/9d5d2e1))
* Add AppVeyor badge. ([7a89376](https://github.com/octalmage/robotjs/commit/7a89376))
* Add desktop keyword. ([b1bc0ef](https://github.com/octalmage/robotjs/commit/b1bc0ef))
* Added special multimedia keycodes + restructured code ([ef4f36d](https://github.com/octalmage/robotjs/commit/ef4f36d))
* Another attempt to fix the accuracy. ([637feed](https://github.com/octalmage/robotjs/commit/637feed))
* Basic appveyor.yml. ([cfc1f78](https://github.com/octalmage/robotjs/commit/cfc1f78))
* Dynamic nan include. ([af0a9f1](https://github.com/octalmage/robotjs/commit/af0a9f1))
* Fix several differences ([523d8e1](https://github.com/octalmage/robotjs/commit/523d8e1))
* Fixed an issue with 'typeString' under Windows. When converting a char to a keycode we now pull the  ([ae58913](https://github.com/octalmage/robotjs/commit/ae58913))
* Fixed the API link ([d5d7c5c](https://github.com/octalmage/robotjs/commit/d5d7c5c))
* Improve conversion accuracy. ([b0258ad](https://github.com/octalmage/robotjs/commit/b0258ad))
* Increase delay to help test reliability. ([66b31b6](https://github.com/octalmage/robotjs/commit/66b31b6))
* Log currentPos to help debug. ([84edc0c](https://github.com/octalmage/robotjs/commit/84edc0c))
* Log mouse positions to help debug. ([49cc03f](https://github.com/octalmage/robotjs/commit/49cc03f))
* New mouse example! ([7121302](https://github.com/octalmage/robotjs/commit/7121302))
* Remove extra newline between badges. ([db69a4f](https://github.com/octalmage/robotjs/commit/db69a4f))
* Remove logging. ([60c86d1](https://github.com/octalmage/robotjs/commit/60c86d1))
* Remove more newlines. ([0522faf](https://github.com/octalmage/robotjs/commit/0522faf))
* Remove newline after example descriptions. ([a1048b9](https://github.com/octalmage/robotjs/commit/a1048b9))
* Remove Waffle.io badge. ([3d193ce](https://github.com/octalmage/robotjs/commit/3d193ce))
* Reorganize badges. ([53adde4](https://github.com/octalmage/robotjs/commit/53adde4))
* Test the latest 4.x.x Node.js version. ([716d3a2](https://github.com/octalmage/robotjs/commit/716d3a2))



## <small>0.3.1 (2015-10-12)</small>

* 0.3.1 ([6ecb2f1](https://github.com/octalmage/robotjs/commit/6ecb2f1))
* Add some comments. ([07a6b11](https://github.com/octalmage/robotjs/commit/07a6b11))
* Allow up or down for second argument. ([718c822](https://github.com/octalmage/robotjs/commit/718c822))
* Bitmap header! ([13cf060](https://github.com/octalmage/robotjs/commit/13cf060))
* Change color to colorAt. ([b413365](https://github.com/octalmage/robotjs/commit/b413365))
* Create and use padHex function. ([547ba93](https://github.com/octalmage/robotjs/commit/547ba93))
* Create imageBuffer manually. ([2425d4a](https://github.com/octalmage/robotjs/commit/2425d4a))
* Destroy bitmap after getting hex. ([e0f616a](https://github.com/octalmage/robotjs/commit/e0f616a))
* Export captureScreen to Node.js. ([5dc3557](https://github.com/octalmage/robotjs/commit/5dc3557))
* Export getColor. ([c27054e](https://github.com/octalmage/robotjs/commit/c27054e))
* Fix key state error. ([1526cf3](https://github.com/octalmage/robotjs/commit/1526cf3))
* Fix mouseToggle ([446d40f](https://github.com/octalmage/robotjs/commit/446d40f))
* Get the correct argument. ([b39d416](https://github.com/octalmage/robotjs/commit/b39d416))
* Get the correct arguments. ([752d2b9](https://github.com/octalmage/robotjs/commit/752d2b9))
* Implemented captureScreen. #13 ([ec4f953](https://github.com/octalmage/robotjs/commit/ec4f953)), closes [#13](https://github.com/octalmage/robotjs/issues/13)
* More accurate types. ([e28cb2a](https://github.com/octalmage/robotjs/commit/e28cb2a))
* No spaces before comment. ([5e81b63](https://github.com/octalmage/robotjs/commit/5e81b63))
* Pass the buffer to createMMBitmap. ([edf9412](https://github.com/octalmage/robotjs/commit/edf9412))
* Pass x and y in with the bitmap object. ([ea719a2](https://github.com/octalmage/robotjs/commit/ea719a2))
* Re-arrange order. ([96d8547](https://github.com/octalmage/robotjs/commit/96d8547))
* Remove unnecessary printf. ([fc9df2a](https://github.com/octalmage/robotjs/commit/fc9df2a))
* RobotJS type. ([634259e](https://github.com/octalmage/robotjs/commit/634259e))
* Started getColor function. ([df737d1](https://github.com/octalmage/robotjs/commit/df737d1))
* Use nan to define strings. ([d2faaf0](https://github.com/octalmage/robotjs/commit/d2faaf0))
* Use Nan::Utf8String ([f114464](https://github.com/octalmage/robotjs/commit/f114464))
* Use x and y. ([54a9cae](https://github.com/octalmage/robotjs/commit/54a9cae))



## 0.3.0 (2015-10-04)

* __MMMouseWheelDirection is the same on all OSes, so we only need to define it once. ([534b4b6](https://github.com/octalmage/robotjs/commit/534b4b6))
* 0.3.0 ([679989c](https://github.com/octalmage/robotjs/commit/679989c))
* Accept "up" or "down" for direction. ([a3346ab](https://github.com/octalmage/robotjs/commit/a3346ab))
* Added header for doubleClick function. ([47de9e1](https://github.com/octalmage/robotjs/commit/47de9e1))
* Added header to moveMouse. ([be280d8](https://github.com/octalmage/robotjs/commit/be280d8))
* Added header to toggleMouse. ([5501e7e](https://github.com/octalmage/robotjs/commit/5501e7e))
* Added link to the blog. ([e2d20c8](https://github.com/octalmage/robotjs/commit/e2d20c8))
* Added microsleep to scrollMouse. ([351176b](https://github.com/octalmage/robotjs/commit/351176b))
* Added missing period. ([be33adc](https://github.com/octalmage/robotjs/commit/be33adc))
* Added printscreen to CheckKeyCodes. ([ae004c8](https://github.com/octalmage/robotjs/commit/ae004c8))
* Added VK_SNAPSHOT to accessible keycodes on Windows. ([61b0584](https://github.com/octalmage/robotjs/commit/61b0584))
* Changed getting started to examples ([cf7c392](https://github.com/octalmage/robotjs/commit/cf7c392))
* Commented this code out for now. ([062be3f](https://github.com/octalmage/robotjs/commit/062be3f)), closes [#50](https://github.com/octalmage/robotjs/issues/50)
* Fixed Examples link. ([21ce43c](https://github.com/octalmage/robotjs/commit/21ce43c))
* Fixed TravisCI config. ([25590a2](https://github.com/octalmage/robotjs/commit/25590a2))
* printscreen is only supported on Windows. ([09d8b21](https://github.com/octalmage/robotjs/commit/09d8b21))
* Reorganize readme ([b009e13](https://github.com/octalmage/robotjs/commit/b009e13))
* uncomment line 43 ([a65488c](https://github.com/octalmage/robotjs/commit/a65488c))
* Update README.md ([0dc62ab](https://github.com/octalmage/robotjs/commit/0dc62ab))
* Updated package.json to use nan 2.0.9. ([945663f](https://github.com/octalmage/robotjs/commit/945663f))
* updated robotjs to compile with nan-2.0.9 ([811a813](https://github.com/octalmage/robotjs/commit/811a813))
* updating travis config file ([11beae0](https://github.com/octalmage/robotjs/commit/11beae0))
* Use nan macro and fix conversion for Windows. ([e8da392](https://github.com/octalmage/robotjs/commit/e8da392))



## <small>0.2.4 (2015-08-28)</small>

* 0.2.4 ([94bdb16](https://github.com/octalmage/robotjs/commit/94bdb16))
* Define MMMouseWheelDirection for Mac. ([1725c10](https://github.com/octalmage/robotjs/commit/1725c10))
* Implemented scrollMouse for Mac. ([ded851c](https://github.com/octalmage/robotjs/commit/ded851c))
* Indent the description. ([630822c](https://github.com/octalmage/robotjs/commit/630822c))
* Linux scrollMouse implementation. ([0403623](https://github.com/octalmage/robotjs/commit/0403623))
* Make global delays configurable. #14 ([dc52bc2](https://github.com/octalmage/robotjs/commit/dc52bc2)), closes [#14](https://github.com/octalmage/robotjs/issues/14)
* MMMouseWheelDirection for Linux. ([58ff736](https://github.com/octalmage/robotjs/commit/58ff736))
* Needed for abs(); ([13a5e15](https://github.com/octalmage/robotjs/commit/13a5e15))
* Show build status for master branch. ([ce6274f](https://github.com/octalmage/robotjs/commit/ce6274f))
* Use global delays for mouse and keyboard functions. ([3cbfc50](https://github.com/octalmage/robotjs/commit/3cbfc50))



## <small>0.2.3 (2015-08-05)</small>

* 0.2.3 ([b6326bc](https://github.com/octalmage/robotjs/commit/b6326bc))
* Added detailed install instructions. ([2e9fc20](https://github.com/octalmage/robotjs/commit/2e9fc20))
* Added doubleClick function. ([f1afaab](https://github.com/octalmage/robotjs/commit/f1afaab))
* Better example titles. ([5711f7b](https://github.com/octalmage/robotjs/commit/5711f7b))
* Fixed mouseClick bug on Windows. ([291890c](https://github.com/octalmage/robotjs/commit/291890c))
* io.js v3 is not supported. ([efb0fd7](https://github.com/octalmage/robotjs/commit/efb0fd7))
* Removed the Windows specific double click. ([d2e33e1](https://github.com/octalmage/robotjs/commit/d2e33e1))
* Sleep for 200 milliseconds instead of 500. ([6e56e16](https://github.com/octalmage/robotjs/commit/6e56e16))
* Updated mouseClick to support double clicks. ([b5db4cb](https://github.com/octalmage/robotjs/commit/b5db4cb))



## <small>0.2.2 (2015-08-01)</small>

* 0.2.2 ([ec1fd14](https://github.com/octalmage/robotjs/commit/ec1fd14))
* Add spaces around assignment operator. ([c1e62de](https://github.com/octalmage/robotjs/commit/c1e62de))
* Added whitespace ([4159921](https://github.com/octalmage/robotjs/commit/4159921))
* Change "specially" to "especially" ([bc1c330](https://github.com/octalmage/robotjs/commit/bc1c330))
* Changed readme wording. ([c2f49a7](https://github.com/octalmage/robotjs/commit/c2f49a7))
* Fix for #15 ([e7983b7](https://github.com/octalmage/robotjs/commit/e7983b7)), closes [#15](https://github.com/octalmage/robotjs/issues/15)
* Fixed minor typo ([673ea16](https://github.com/octalmage/robotjs/commit/673ea16))
* MouseScroll feature addition ([4052946](https://github.com/octalmage/robotjs/commit/4052946))
* Something like this :cheers: ([c632f78](https://github.com/octalmage/robotjs/commit/c632f78))
* Update README.md ([38ec22c](https://github.com/octalmage/robotjs/commit/38ec22c))
* Update README.md ([34421b4](https://github.com/octalmage/robotjs/commit/34421b4))
* Update README.md ([2f67943](https://github.com/octalmage/robotjs/commit/2f67943))
* Update README.md ([1f2dd31](https://github.com/octalmage/robotjs/commit/1f2dd31))
* Update robotjs.cc ([fb4198c](https://github.com/octalmage/robotjs/commit/fb4198c))



## <small>0.2.1 (2015-07-26)</small>

* 0.2.1 ([b0dc3bc](https://github.com/octalmage/robotjs/commit/b0dc3bc))
* Confirm that the mouse is in the correct location. ([8081dd1](https://github.com/octalmage/robotjs/commit/8081dd1))
* Move the mouse in the moveMouse test. ([cf93363](https://github.com/octalmage/robotjs/commit/cf93363))
* Sleep after mouse events. #14 ([939518a](https://github.com/octalmage/robotjs/commit/939518a)), closes [#14](https://github.com/octalmage/robotjs/issues/14)
* Test moving the mouse. ([3043db2](https://github.com/octalmage/robotjs/commit/3043db2))



## 0.2.0 (2015-07-25)

* 0.2.0 ([cac32ba](https://github.com/octalmage/robotjs/commit/cac32ba))
* Add Windows support. ([bcc8164](https://github.com/octalmage/robotjs/commit/bcc8164))
* Added link to list of projects. ([491f675](https://github.com/octalmage/robotjs/commit/491f675))
* Fixed formatting. ([7222506](https://github.com/octalmage/robotjs/commit/7222506))
* getPixelColor bug fix. ([20c9a19](https://github.com/octalmage/robotjs/commit/20c9a19))
* Include the portable snprintf. ([fc4016d](https://github.com/octalmage/robotjs/commit/fc4016d))
* Line up arguments. ([056b6b1](https://github.com/octalmage/robotjs/commit/056b6b1))
* Make the portable snprintf C++ compatible. ([5e1dadf](https://github.com/octalmage/robotjs/commit/5e1dadf))
* No ms_stdint.h. ([12c1ff7](https://github.com/octalmage/robotjs/commit/12c1ff7))
* Remove sponsor line. ([547aa0c](https://github.com/octalmage/robotjs/commit/547aa0c))
* RobotJS is now cross platform! ([6561826](https://github.com/octalmage/robotjs/commit/6561826))
* Run the screen tests. ([64e9e4a](https://github.com/octalmage/robotjs/commit/64e9e4a))
* Tests for getPixelColor and getScreenSize. ([790c3c6](https://github.com/octalmage/robotjs/commit/790c3c6))
* These throw errors, need to come back to this. ([5bc2eb2](https://github.com/octalmage/robotjs/commit/5bc2eb2))
* Typecast from int to double. ([8730e94](https://github.com/octalmage/robotjs/commit/8730e94))
* Unnecessary and causes warnings. ([25c3b84](https://github.com/octalmage/robotjs/commit/25c3b84))
* Updated to match code style. ([1208cf8](https://github.com/octalmage/robotjs/commit/1208cf8))
* Use cross platform microsleep instead of mssleep. ([a511724](https://github.com/octalmage/robotjs/commit/a511724))



## <small>0.1.4 (2015-07-19)</small>

* 0.1.4 ([c982f0a](https://github.com/octalmage/robotjs/commit/c982f0a))
* Added space to keycode list. ([523cf3b](https://github.com/octalmage/robotjs/commit/523cf3b))
* Added space to keyTap and keyToggle. ([589ca78](https://github.com/octalmage/robotjs/commit/589ca78))
* Corrected spacebar keycode. ([e935825](https://github.com/octalmage/robotjs/commit/e935825))
* Fixed buffer overflow. Closes #19. ([1da780f](https://github.com/octalmage/robotjs/commit/1da780f)), closes [#19](https://github.com/octalmage/robotjs/issues/19)
* Updating description. ([e2541ef](https://github.com/octalmage/robotjs/commit/e2541ef))



## <small>0.1.3 (2015-07-18)</small>

* 0.1.3 ([71dceb0](https://github.com/octalmage/robotjs/commit/71dceb0))
* add framework to accept modifiers on keys. set up keytoggle. ([527f9d9](https://github.com/octalmage/robotjs/commit/527f9d9))
* Added "maintained by" line. ([fcd4c98](https://github.com/octalmage/robotjs/commit/fcd4c98))
* Added missing periods. ([431abe5](https://github.com/octalmage/robotjs/commit/431abe5))
* Added note about a sponsor. ([10a171e](https://github.com/octalmage/robotjs/commit/10a171e))
* Added space before License header. ([eb64d14](https://github.com/octalmage/robotjs/commit/eb64d14))
* Changed wording to hint that Linux is supported. ([cd38d0e](https://github.com/octalmage/robotjs/commit/cd38d0e))
* Fixed code style. ([36c5297](https://github.com/octalmage/robotjs/commit/36c5297))
* Fixed style of keyToggle. ([0ba1797](https://github.com/octalmage/robotjs/commit/0ba1797))
* fixed typo ([2a42cc3](https://github.com/octalmage/robotjs/commit/2a42cc3))
* Fixed typo ([c2f6ee0](https://github.com/octalmage/robotjs/commit/c2f6ee0))
* fixes keyTap not releasing. allows for multiple keypresses or keytaps in a row by adding sleep funct ([47ff4a2](https://github.com/octalmage/robotjs/commit/47ff4a2))
* get keytap working for cmd-tab, started on keytoggle ([ebd2bb4](https://github.com/octalmage/robotjs/commit/ebd2bb4))
* More spaces to tabs. ([d3f03d0](https://github.com/octalmage/robotjs/commit/d3f03d0))
* removed wiki link ([8c221d9](https://github.com/octalmage/robotjs/commit/8c221d9))
* Spaces to tabs. ([72a4e94](https://github.com/octalmage/robotjs/commit/72a4e94))
* Switched to tab. ([16cfd6c](https://github.com/octalmage/robotjs/commit/16cfd6c))
* Update README.md ([b9c57a4](https://github.com/octalmage/robotjs/commit/b9c57a4))
* Updated readme with new description. ([41b762c](https://github.com/octalmage/robotjs/commit/41b762c))
* Updating progress chart for keyboard. ([6e89410](https://github.com/octalmage/robotjs/commit/6e89410))



## <small>0.1.2 (2015-04-26)</small>

* 0.1.2 ([82df310](https://github.com/octalmage/robotjs/commit/82df310))
* A travis configuration that I reckon will work (including xvfb) ([9246dac](https://github.com/octalmage/robotjs/commit/9246dac))
* Add required apt packages ([779d238](https://github.com/octalmage/robotjs/commit/779d238))
* Add xdisplay to the compilation list for linux ([36d1951](https://github.com/octalmage/robotjs/commit/36d1951))
* Added build status. ([30f753d](https://github.com/octalmage/robotjs/commit/30f753d))
* Added compiler warnings as per autopy ([b04a3bf](https://github.com/octalmage/robotjs/commit/b04a3bf))
* Added getScreenSize, closes #25. ([d94ca48](https://github.com/octalmage/robotjs/commit/d94ca48)), closes [#25](https://github.com/octalmage/robotjs/issues/25)
* Added link to wiki. ([7ad2129](https://github.com/octalmage/robotjs/commit/7ad2129))
* Added linux link settings ([7e705e2](https://github.com/octalmage/robotjs/commit/7e705e2))
* Added Tab key to keyTap ([d1e7b01](https://github.com/octalmage/robotjs/commit/d1e7b01))
* MIT bitches. ([f415f54](https://github.com/octalmage/robotjs/commit/f415f54))
* pandering to my OCD - sorry :/ ([099a21d](https://github.com/octalmage/robotjs/commit/099a21d))
* Reformat to use single quotes as that seems to be the standard for gyp ([b70a67c](https://github.com/octalmage/robotjs/commit/b70a67c))
* shadow too noisy when working with V8 ([4cdeb86](https://github.com/octalmage/robotjs/commit/4cdeb86))
* Some basic tests to get the ball rolling ([86714bf](https://github.com/octalmage/robotjs/commit/86714bf))
* This was suppose to be MIT. ([aca285b](https://github.com/octalmage/robotjs/commit/aca285b))
* Updated progress chart. ([1da2e14](https://github.com/octalmage/robotjs/commit/1da2e14))
* Use tape for testing ([4f4b043](https://github.com/octalmage/robotjs/commit/4f4b043))



## <small>0.1.1 (2015-02-06)</small>

* Added additional keys for keyTap. ([1895133](https://github.com/octalmage/robotjs/commit/1895133))
* Fixed extern location. ([581cbdc](https://github.com/octalmage/robotjs/commit/581cbdc))
* Updated keyTap example. ([7d056e8](https://github.com/octalmage/robotjs/commit/7d056e8))
* Updated keyTap for correct use. Closes #3. ([ec8adb9](https://github.com/octalmage/robotjs/commit/ec8adb9)), closes [#3](https://github.com/octalmage/robotjs/issues/3)
* Updated to version 0.1.1. ([764dd11](https://github.com/octalmage/robotjs/commit/764dd11))



## 0.1.0 (2015-02-04)

* Add periods to all errors. ([b11648a](https://github.com/octalmage/robotjs/commit/b11648a))
* Added mouseToggle, closes #8. ([3f4918b](https://github.com/octalmage/robotjs/commit/3f4918b)), closes [#8](https://github.com/octalmage/robotjs/issues/8)
* Added right/middle click support, closes #6. ([2fc100a](https://github.com/octalmage/robotjs/commit/2fc100a)), closes [#6](https://github.com/octalmage/robotjs/issues/6)
* Changed button variable name. ([6113ec5](https://github.com/octalmage/robotjs/commit/6113ec5))
* Uniform syntax. ([ca191c1](https://github.com/octalmage/robotjs/commit/ca191c1))
* Version 0.1.0. ([245a862](https://github.com/octalmage/robotjs/commit/245a862))



## <small>0.0.5 (2015-02-04)</small>

* Added additional examples. ([ba5cb80](https://github.com/octalmage/robotjs/commit/ba5cb80))
* Added getPixelColor. ([f250355](https://github.com/octalmage/robotjs/commit/f250355))
* Added more badges. ([39ef2f7](https://github.com/octalmage/robotjs/commit/39ef2f7))
* Added mouseMoveSmooth. ([ecdd16a](https://github.com/octalmage/robotjs/commit/ecdd16a))
* Added waffle.io badge! ([1488a75](https://github.com/octalmage/robotjs/commit/1488a75))
* Converted mouseClick function. ([87fe58e](https://github.com/octalmage/robotjs/commit/87fe58e))
* Converted rest of code to nan. ([d44b1a2](https://github.com/octalmage/robotjs/commit/d44b1a2))
* Converted typeString to use nan. Closes #1. ([ea62efa](https://github.com/octalmage/robotjs/commit/ea62efa)), closes [#1](https://github.com/octalmage/robotjs/issues/1)
* Fixed code style. ([de175e7](https://github.com/octalmage/robotjs/commit/de175e7))
* Fixed heading syntax. ([d2102c8](https://github.com/octalmage/robotjs/commit/d2102c8))
* Fixed plans. ([17d7f24](https://github.com/octalmage/robotjs/commit/17d7f24))
* Include screen related code. ([ea69110](https://github.com/octalmage/robotjs/commit/ea69110))
* Link to syntax issue. ([9b8b765](https://github.com/octalmage/robotjs/commit/9b8b765))
* Linked to nan issue. ([9f7cdba](https://github.com/octalmage/robotjs/commit/9f7cdba))
* nan rewrite done! ([f217e9a](https://github.com/octalmage/robotjs/commit/f217e9a))
* Removed node_modules. ([b4f2085](https://github.com/octalmage/robotjs/commit/b4f2085))
* Removed unused functions. ([c9509bf](https://github.com/octalmage/robotjs/commit/c9509bf))
* Removed unused helper functions. ([6f60b53](https://github.com/octalmage/robotjs/commit/6f60b53))
* Updated to be compatible with C++. ([cacd8f9](https://github.com/octalmage/robotjs/commit/cacd8f9))
* Updated version to 0.0.4. ([9f97237](https://github.com/octalmage/robotjs/commit/9f97237))
* Updated version to 0.0.5. ([e981548](https://github.com/octalmage/robotjs/commit/e981548))



## <small>0.0.3 (2015-01-20)</small>

* Added build instructions. ([4a9e8c6](https://github.com/octalmage/robotjs/commit/4a9e8c6))
* Added keyTap and typeString, started on bitmap. ([8cb04e5](https://github.com/octalmage/robotjs/commit/8cb04e5))
* Added more details about plans. ([97ead45](https://github.com/octalmage/robotjs/commit/97ead45))
* Added nan module. ([bcf1111](https://github.com/octalmage/robotjs/commit/bcf1111))
* Added note about progress. ([c5da2d2](https://github.com/octalmage/robotjs/commit/c5da2d2))
* Added story section. ([0ff5407](https://github.com/octalmage/robotjs/commit/0ff5407))
* Added Window module to progress table. ([b9da81d](https://github.com/octalmage/robotjs/commit/b9da81d))
* Cleaning up the code, moving window code to new branch. ([16f67af](https://github.com/octalmage/robotjs/commit/16f67af))
* Excluding build/ ([5ba0e11](https://github.com/octalmage/robotjs/commit/5ba0e11))
* Experimenting with Window manipulation. ([ea34961](https://github.com/octalmage/robotjs/commit/ea34961))
* Including nan. ([1f407d8](https://github.com/octalmage/robotjs/commit/1f407d8))
* Moved screen related functions to it's own branch. ([2b4564d](https://github.com/octalmage/robotjs/commit/2b4564d))
* New release. ([0361eb4](https://github.com/octalmage/robotjs/commit/0361eb4))
* Removed screen related files from build. ([919fc80](https://github.com/octalmage/robotjs/commit/919fc80))
* Started converting to nan. ([19261d7](https://github.com/octalmage/robotjs/commit/19261d7))
* Update README.md ([cbeb5af](https://github.com/octalmage/robotjs/commit/cbeb5af))
* Updated dependencies to include nan. ([106788b](https://github.com/octalmage/robotjs/commit/106788b))
* Updated install instructions. ([1d1d841](https://github.com/octalmage/robotjs/commit/1d1d841))
* Updated package version. ([d9b1d98](https://github.com/octalmage/robotjs/commit/d9b1d98))
* Updating progress. ([29a293d](https://github.com/octalmage/robotjs/commit/29a293d))



## <small>0.0.2 (2014-09-01)</small>

* Changed name to RobotJS. This is final! ([9651d26](https://github.com/octalmage/robotjs/commit/9651d26))
* Fixed install instructions. ([8a94cc6](https://github.com/octalmage/robotjs/commit/8a94cc6))
* Published to NPM! ([1401e63](https://github.com/octalmage/robotjs/commit/1401e63))
* Updated README.md ([5e05785](https://github.com/octalmage/robotjs/commit/5e05785))



## <small>0.0.1 (2014-09-01)</small>

* Added build instructions. ([98ef54e](https://github.com/octalmage/robotjs/commit/98ef54e))
* Added mouseClick function. ([e4ba3c0](https://github.com/octalmage/robotjs/commit/e4ba3c0))
* Added progress. ([7f2090d](https://github.com/octalmage/robotjs/commit/7f2090d))
* Create README.md ([d78304b](https://github.com/octalmage/robotjs/commit/d78304b))
* Getting ready for NPM. ([33f7d82](https://github.com/octalmage/robotjs/commit/33f7d82))
* Got some basic mouse functions working. ([d84e253](https://github.com/octalmage/robotjs/commit/d84e253))
* Initial commit. ([38c1700](https://github.com/octalmage/robotjs/commit/38c1700))
* Moved some files around. ([aad9e6d](https://github.com/octalmage/robotjs/commit/aad9e6d))
* Removed unnecessary files. ([d2f1a3d](https://github.com/octalmage/robotjs/commit/d2f1a3d))
* Update README.md ([4cee4c5](https://github.com/octalmage/robotjs/commit/4cee4c5))



