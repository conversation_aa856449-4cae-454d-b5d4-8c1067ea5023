{"name": "voice-pilot", "version": "1.0.0", "description": "AI-powered voice assistant for global text input", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "test": "jest"}, "keywords": ["electron", "voice", "ai", "automation", "speech-to-text"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0", "jest": "^29.0.0"}, "dependencies": {"robotjs": "^0.6.0", "node-record-lpcm16": "^1.0.1", "axios": "^1.6.0", "form-data": "^4.0.0", "electron-store": "^8.1.0"}, "build": {"appId": "com.voicepilot.app", "productName": "VoicePilot", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}