const fs = require('fs');
const path = require('path');
const os = require('os');
const { BrowserWindow } = require('electron');

class VoiceRecorder {
  constructor() {
    this.isRecording = false;
    this.audioWindow = null;

    // 支援在Electron和Node.js環境中運行
    try {
      const { app } = require('electron');
      this.tempDir = path.join(app.getPath('temp'), 'voicepilot');
    } catch (error) {
      // 如果不在Electron環境中，使用系統臨時目錄
      this.tempDir = path.join(os.tmpdir(), 'voicepilot');
    }

    // 確保臨時目錄存在
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  async startRecording() {
    return new Promise((resolve, reject) => {
      try {
        if (this.isRecording) {
          reject(new Error('Already recording'));
          return;
        }

        this.isRecording = true;
        console.log('Recording started...');

        // 暫時返回成功，實際錄音功能需要在渲染程序中實現
        resolve();

      } catch (error) {
        this.isRecording = false;
        reject(error);
      }
    });
  }

  async stopRecording() {
    return new Promise((resolve, reject) => {
      try {
        if (!this.isRecording) {
          reject(new Error('Not currently recording'));
          return;
        }

        this.isRecording = false;
        console.log('Recording stopped...');

        // 暫時返回一個模擬的音訊緩衝區
        const mockAudioBuffer = Buffer.from('mock audio data for testing');
        resolve(mockAudioBuffer);

      } catch (error) {
        this.isRecording = false;
        reject(error);
      }
    });
  }

  createWavBuffer(pcmBuffer) {
    const sampleRate = 16000;
    const numChannels = 1;
    const bitsPerSample = 16;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = pcmBuffer.length;
    const fileSize = 36 + dataSize;

    const wavBuffer = Buffer.alloc(44 + dataSize);
    let offset = 0;

    // RIFF header
    wavBuffer.write('RIFF', offset); offset += 4;
    wavBuffer.writeUInt32LE(fileSize, offset); offset += 4;
    wavBuffer.write('WAVE', offset); offset += 4;

    // fmt chunk
    wavBuffer.write('fmt ', offset); offset += 4;
    wavBuffer.writeUInt32LE(16, offset); offset += 4; // chunk size
    wavBuffer.writeUInt16LE(1, offset); offset += 2;  // audio format (PCM)
    wavBuffer.writeUInt16LE(numChannels, offset); offset += 2;
    wavBuffer.writeUInt32LE(sampleRate, offset); offset += 4;
    wavBuffer.writeUInt32LE(byteRate, offset); offset += 4;
    wavBuffer.writeUInt16LE(blockAlign, offset); offset += 2;
    wavBuffer.writeUInt16LE(bitsPerSample, offset); offset += 2;

    // data chunk
    wavBuffer.write('data', offset); offset += 4;
    wavBuffer.writeUInt32LE(dataSize, offset); offset += 4;
    pcmBuffer.copy(wavBuffer, offset);

    return wavBuffer;
  }

  // 儲存音訊檔案用於除錯
  async saveAudioFile(buffer, filename = 'debug_audio.wav') {
    const filePath = path.join(this.tempDir, filename);
    fs.writeFileSync(filePath, buffer);
    console.log(`Audio saved to: ${filePath}`);
    return filePath;
  }

  // 清理臨時檔案
  cleanup() {
    try {
      if (fs.existsSync(this.tempDir)) {
        const files = fs.readdirSync(this.tempDir);
        files.forEach(file => {
          fs.unlinkSync(path.join(this.tempDir, file));
        });
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }
}

module.exports = VoiceRecorder;
