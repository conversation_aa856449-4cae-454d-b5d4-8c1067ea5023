// 測試音訊錄製功能
const VoiceRecorder = require('./src/services/VoiceRecorder');

async function testAudioRecording() {
  console.log('Testing audio recording...');
  
  const recorder = new VoiceRecorder();
  
  try {
    console.log('Starting recording for 3 seconds...');
    await recorder.startRecording();
    
    // 錄製3秒
    setTimeout(async () => {
      try {
        console.log('Stopping recording...');
        const audioBuffer = await recorder.stopRecording();
        console.log(`Recording completed. Buffer size: ${audioBuffer.length} bytes`);
        
        // 儲存測試檔案
        const filePath = await recorder.saveAudioFile(audioBuffer, 'test_recording.wav');
        console.log(`Test audio saved to: ${filePath}`);
        
      } catch (error) {
        console.error('Error stopping recording:', error);
      }
    }, 3000);
    
  } catch (error) {
    console.error('Error starting recording:', error);
  }
}

testAudioRecording();
